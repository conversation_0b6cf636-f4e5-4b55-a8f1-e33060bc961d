/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@400;500;600;700&display=swap");

:root {
  /* Diamond Manufacturing Theme Colors */
  --color-primary: #1a237e; /* Deep royal blue */
  --color-primary-light: #4a51a5; /* Lighter blue */
  --color-primary-dark: #0d1642; /* Darker blue */

  --color-secondary: #E8F4FD; /* Diamond white-blue */
  --color-secondary-light: #F0F8FF; /* Light diamond blue */
  --color-secondary-dark: #B8D4F0; /* Darker diamond blue */

  --color-accent-1: #e5e4e2; /* Silver/platinum */
  --color-accent-2: #f5f5f5; /* Lighter silver */
  --color-accent-3: #303f9f; /* Medium blue */
  --color-accent-4: #ffffff; /* Pure white */

  --color-text: #262626; /* Rich black for main text */
  --color-text-light: #4d4d4d; /* Dark gray for body text */
  --color-text-lighter: #757575; /* Medium gray for secondary text */

  --color-background: #ffffff; /* Pure white */
  --color-background-alt: #f5f7ff; /* Very light blue */
  --color-background-card: #ffffff; /* White for cards */

  --color-border: #e5e5e5; /* Light gray for borders */
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* Feedback Colors */
  --color-success: #4a7c59;
  --color-warning: #d4af37;
  --color-error: #a02c2c;
  --color-info: #1a237e;

  /* Gradients */
  --gradient-diamond: linear-gradient(
    135deg,
    #E8F4FD 0%,
    #FFFFFF 50%,
    #E8F4FD 100%
  );
  --gradient-blue: linear-gradient(
    135deg,
    #1a237e 0%,
    #303f9f 50%,
    #1a237e 100%
  );
  --gradient-silver: linear-gradient(
    135deg,
    #c0c0c0 0%,
    #e5e4e2 50%,
    #c0c0c0 100%
  );
  --gradient-blue-gold: linear-gradient(135deg, #1a237e 0%, #d4af37 100%);
  --gradient-white-diamond: linear-gradient(135deg, #ffffff 0%, #E8F4FD 100%);
  --gradient-elegant: linear-gradient(
    to right,
    #0d1642,
    #1a237e,
    #303f9f,
    #4a51a5,
    #7986cb
  );

  /* Typography */
  --font-heading: "Playfair Display", serif;
  --font-body: "Montserrat", sans-serif;
  --font-accent: "Cormorant Garamond", serif;

  /* Spacing */
  --spacing-1: 0.25rem; /* 4px */
  --spacing-2: 0.5rem; /* 8px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-4: 1rem; /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem; /* 24px */
  --spacing-8: 2rem; /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-12: 3rem; /* 48px */
  --spacing-16: 4rem; /* 64px */
  --spacing-20: 5rem; /* 80px */

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.05);
  --shadow-gold: 0 5px 15px rgba(212, 175, 55, 0.15);
  --shadow-silver: 0 5px 15px rgba(192, 192, 192, 0.15);

  /* Border Radius */
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 6px;
  --border-radius-xl: 8px;
  --border-radius-full: 9999px;

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition-default: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Dark Theme Class */
.dark-theme {
  --color-primary: #d4af37; /* Gold becomes primary in dark mode */
  --color-primary-light: #e6c55c; /* Light gold */
  --color-primary-dark: #b8971f; /* Darker gold */

  --color-secondary: #303f9f; /* Deep blue becomes secondary */
  --color-secondary-light: #4a51a5; /* Lighter blue */
  --color-secondary-dark: #1a237e; /* Darker blue */

  --color-text: #f8f8f8; /* Off-white for main text */
  --color-text-light: #d9d9d9; /* Light gray for body text */
  --color-text-lighter: #a0a0a0; /* Medium gray for secondary text */

  --color-background: #121212; /* Very dark gray */
  --color-background-alt: #1a1a2e; /* Dark blue-gray */
  --color-background-card: #1e1e2e; /* Dark blue-gray for cards */

  --color-border: #333333; /* Dark gray for borders */
  --color-shadow: rgba(0, 0, 0, 0.3);

  /* Gradients for dark mode */
  --gradient-gold: linear-gradient(
    135deg,
    #b8971f 0%,
    #d4af37 50%,
    #b8971f 100%
  );
  --gradient-blue: linear-gradient(
    135deg,
    #0d1642 0%,
    #1a237e 50%,
    #0d1642 100%
  );
  --gradient-silver: linear-gradient(
    135deg,
    #505050 0%,
    #8e8e8e 50%,
    #505050 100%
  );
  --gradient-blue-gold: linear-gradient(135deg, #0d1642 0%, #b8971f 100%);
  --gradient-elegant: linear-gradient(
    to right,
    #000000,
    #0d1642,
    #1a237e,
    #303f9f,
    #4a51a5
  );

  /* Shadows for dark mode */
  --shadow-gold: 0 5px 15px rgba(212, 175, 55, 0.25);
  --shadow-silver: 0 5px 15px rgba(142, 142, 142, 0.25);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  color: var(--color-text);
  line-height: 1.5;
  background-color: var(--color-background);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-default),
    color var(--transition-default);
  overflow-x: hidden;
  min-height: 100vh;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--color-text);
}

h1 {
  font-size: 3rem;
  letter-spacing: -0.025em;
}

h2 {
  font-size: 2.25rem;
  letter-spacing: -0.025em;
}

h3 {
  font-size: 1.875rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.625;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-secondary);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  cursor: pointer;
  font-family: var(--font-body);
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section {
  padding: var(--spacing-16) 0;
}

.section-title {
  position: relative;
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: var(--gradient-diamond);
  border-radius: 1px;
}

.text-gradient-diamond {
  background: var(--gradient-diamond);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-silver {
  background: var(--gradient-silver);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-gold {
  background: linear-gradient(135deg, #d4af37 0%, #f7e9b7 50%, #d4af37 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.card {
  background-color: var(--color-background-card);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--transition-default),
    box-shadow var(--transition-default);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Responsive adjustments */
@media (max-width: 1280px) {
  .container {
    max-width: 1024px;
  }
}

@media (max-width: 1024px) {
  .container {
    max-width: 768px;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.75rem;
  }

  .section {
    padding: var(--spacing-12) 0;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 640px;
  }

  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.875rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .section {
    padding: var(--spacing-8) 0;
  }
}

@media (max-width: 640px) {
  .container {
    max-width: 100%;
    padding: 0 var(--spacing-4);
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}
