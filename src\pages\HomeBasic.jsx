import React from "react";
import {
  Typo<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  St<PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "antd";
import {
  ArrowRightOutlined,
  CheckCircleFilled,
  HistoryOutlined,
  TeamOutlined,
  GlobalOutlined,
  ToolOutlined,
  SafetyCertificateOutlined,
  StarFilled,
  PhoneOutlined,
} from "@ant-design/icons";
import styled from "styled-components";

const { Title, Text, Paragraph } = Typography;

// Simple Styled Components
const PageContainer = styled.div`
  background: #ffffff;
  min-height: 100vh;
`;

const Section = styled.section`
  padding: 80px 0;
  
  @media (max-width: 768px) {
    padding: 60px 0;
  }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  
  @media (max-width: 768px) {
    padding: 0 16px;
  }
`;

const HeroSection = styled(Section)`
  background: linear-gradient(135deg, #1a237e 0%, #000051 100%);
  color: white;
  text-align: center;
  padding: 120px 0;
  margin-top: 80px;
`;

const Home = () => {
  return (
    <PageContainer>
      {/* Hero Section */}
      <HeroSection>
        <Container>
          <Title
            level={1}
            style={{
              color: "#ffffff",
              fontSize: "3.5rem",
              fontWeight: 900,
              marginBottom: "30px",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            Ramavatar Gems
          </Title>
          <Title
            level={2}
            style={{
              color: "#d4af37",
              fontSize: "2.5rem",
              fontWeight: 700,
              marginBottom: "20px",
            }}
          >
            Diamond Jewelry Manufacturing Excellence
          </Title>
          <Text
            style={{
              display: "block",
              fontSize: "1.3rem",
              color: "#ffffff",
              marginBottom: "40px",
              maxWidth: "800px",
              margin: "0 auto 40px",
              lineHeight: 1.6,
            }}
          >
            Crafting exquisite diamond jewelry with precision and artistry since 1982. 
            Where tradition meets innovation in every sparkling creation.
          </Text>
          <Space size="large">
            <AntButton
              type="primary"
              size="large"
              style={{
                background: "#d4af37",
                borderColor: "#d4af37",
                fontSize: "1.1rem",
                height: "auto",
                padding: "12px 30px",
                borderRadius: "25px",
              }}
            >
              <ToolOutlined style={{ marginRight: 8 }} />
              Explore Our Process
            </AntButton>
            <AntButton
              size="large"
              style={{
                borderColor: "#ffffff",
                color: "#ffffff",
                fontSize: "1.1rem",
                height: "auto",
                padding: "12px 30px",
                borderRadius: "25px",
                background: "transparent",
              }}
            >
              <PhoneOutlined style={{ marginRight: 8 }} />
              Contact Us
            </AntButton>
          </Space>
        </Container>
      </HeroSection>

      {/* Statistics Section */}
      <Section style={{ background: "#f8f9fa" }}>
        <Container>
          <Title
            level={2}
            style={{
              textAlign: "center",
              color: "#333",
              fontSize: "2.5rem",
              fontWeight: 800,
              marginBottom: "60px",
            }}
          >
            Our <span style={{ color: "#d4af37" }}>Legacy</span>
          </Title>
          <Row gutter={[32, 32]}>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "#ffffff",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <HistoryOutlined
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "16px",
                  }}
                />
                <Statistic
                  title="Years of Excellence"
                  value="40+"
                  valueStyle={{
                    fontSize: "2.5rem",
                    fontWeight: 800,
                    color: "#1a237e",
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "#ffffff",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <TeamOutlined
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "16px",
                  }}
                />
                <Statistic
                  title="Master Craftsmen"
                  value="50+"
                  valueStyle={{
                    fontSize: "2.5rem",
                    fontWeight: 800,
                    color: "#1a237e",
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "#ffffff",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <GlobalOutlined
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "16px",
                  }}
                />
                <Statistic
                  title="Countries Served"
                  value="25+"
                  valueStyle={{
                    fontSize: "2.5rem",
                    fontWeight: 800,
                    color: "#1a237e",
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "#ffffff",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <StarFilled
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "16px",
                  }}
                />
                <Statistic
                  title="Satisfied Clients"
                  value="10,000+"
                  valueStyle={{
                    fontSize: "2.5rem",
                    fontWeight: 800,
                    color: "#1a237e",
                  }}
                />
              </Card>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Services Section */}
      <Section>
        <Container>
          <Title
            level={2}
            style={{
              textAlign: "center",
              color: "#333",
              fontSize: "2.5rem",
              fontWeight: 800,
              marginBottom: "60px",
            }}
          >
            Our <span style={{ color: "#d4af37" }}>Services</span>
          </Title>
          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <Card
                style={{
                  height: "100%",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <ToolOutlined
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "20px",
                  }}
                />
                <Title level={4} style={{ color: "#333", marginBottom: "15px" }}>
                  Diamond Manufacturing
                </Title>
                <Paragraph style={{ color: "#666", fontSize: "1rem" }}>
                  Expert diamond cutting, polishing, and setting services with precision 
                  craftsmanship that brings out the natural beauty of each stone.
                </Paragraph>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card
                style={{
                  height: "100%",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <SafetyCertificateOutlined
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "20px",
                  }}
                />
                <Title level={4} style={{ color: "#333", marginBottom: "15px" }}>
                  Quality Assurance
                </Title>
                <Paragraph style={{ color: "#666", fontSize: "1rem" }}>
                  Rigorous quality control processes ensure every piece meets our 
                  exceptional standards for brilliance, clarity, and craftsmanship.
                </Paragraph>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card
                style={{
                  height: "100%",
                  borderRadius: "15px",
                  boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
                  border: "none",
                }}
              >
                <CheckCircleFilled
                  style={{
                    fontSize: "3rem",
                    color: "#d4af37",
                    marginBottom: "20px",
                  }}
                />
                <Title level={4} style={{ color: "#333", marginBottom: "15px" }}>
                  Custom Design
                </Title>
                <Paragraph style={{ color: "#666", fontSize: "1rem" }}>
                  Bespoke jewelry design services that transform your vision into 
                  stunning reality with personalized attention to every detail.
                </Paragraph>
              </Card>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Call to Action */}
      <Section style={{ background: "#1a237e", color: "white" }}>
        <Container style={{ textAlign: "center" }}>
          <Title
            level={2}
            style={{
              color: "#ffffff",
              fontSize: "2.5rem",
              fontWeight: 800,
              marginBottom: "20px",
            }}
          >
            Ready to Create Something Beautiful?
          </Title>
          <Paragraph
            style={{
              color: "#ffffff",
              fontSize: "1.2rem",
              marginBottom: "40px",
              maxWidth: "600px",
              margin: "0 auto 40px",
            }}
          >
            Let our master craftsmen bring your diamond jewelry dreams to life with 
            unparalleled expertise and attention to detail.
          </Paragraph>
          <AntButton
            type="primary"
            size="large"
            style={{
              background: "#d4af37",
              borderColor: "#d4af37",
              fontSize: "1.2rem",
              height: "auto",
              padding: "15px 40px",
              borderRadius: "30px",
            }}
          >
            <ArrowRightOutlined style={{ marginRight: 8 }} />
            Get Started Today
          </AntButton>
        </Container>
      </Section>
    </PageContainer>
  );
};

export default Home;
