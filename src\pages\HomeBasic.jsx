import React from "react";
import {
  Typo<PERSON>,
  <PERSON>,
  <PERSON>,
  Card,
  Statistic,
  Space,
  But<PERSON> as <PERSON>t<PERSON>utton,
  Carousel,
  Image,
  Badge,
  Rate,
} from "antd";
import {
  ArrowRightOutlined,
  CheckCircleFilled,
  HistoryOutlined,
  TeamOutlined,
  GlobalOutlined,
  ToolOutlined,
  SafetyCertificateOutlined,
  StarFilled,
  PhoneOutlined,
  DiamondOutlined,
  CrownOutlined,
  TrophyOutlined,
  HeartOutlined,
  EnvironmentOutlined,
  PlayCircleOutlined,
  ShoppingOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import styled, { keyframes } from "styled-components";
import { useTheme } from "../context/ThemeContext";
import RamavatargemsLogo from "../components/logo/RamavatargemsLogo";

const { Title, Text, Paragraph } = Typography;

// Animations
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
`;

const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
`;

// Professional Styled Components
const PageContainer = styled.div`
  background: var(--color-background);
  min-height: 100vh;
  transition: background-color 0.3s ease;
`;

const Section = styled.section`
  padding: 120px 0;
  position: relative;

  @media (max-width: 768px) {
    padding: 80px 0;
  }

  @media (max-width: 480px) {
    padding: 60px 0;
  }
`;

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    padding: 0 16px;
  }
`;

const HeroSection = styled(Section)`
  height: calc(100vh - 80px);
  min-height: 700px;
  max-height: 900px;
  padding: 0;
  position: relative;
  overflow: hidden;
  margin-top: 80px;

  @media (max-width: 768px) {
    height: calc(100vh - 70px);
    min-height: 600px;
    margin-top: 70px;
  }
`;

const HeroCarousel = styled(Carousel)`
  height: 100%;

  .slick-dots {
    bottom: 40px;
    z-index: 10;

    li button {
      width: 60px;
      height: 4px;
      border-radius: 2px;
      background: rgba(255, 255, 255, 0.4);
    }

    li.slick-active button {
      background: #d4af37;
      box-shadow: 0 0 20px rgba(212, 175, 55, 0.6);
    }
  }

  .slick-arrow {
    width: 60px;
    height: 60px;
    z-index: 10;

    &:before {
      font-size: 24px;
      color: white;
    }

    @media (max-width: 768px) {
      display: none !important;
    }
  }
`;

const HeroSlide = styled.div`
  height: calc(100vh - 80px);
  min-height: 700px;
  max-height: 900px;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.7) 0%,
      rgba(26, 35, 126, 0.6) 30%,
      rgba(0, 0, 0, 0.8) 100%
    );
    z-index: 1;
  }

  @media (max-width: 768px) {
    height: calc(100vh - 70px);
    min-height: 600px;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 10;
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: ${fadeInUp} 1s ease-out;

  @media (max-width: 768px) {
    padding: 30px 20px;
    margin: 0 20px;
  }
`;

const Home = () => {
  const { isDarkMode } = useTheme();

  return (
    <PageContainer>
      {/* Hero Section with Carousel */}
      <HeroSection>
        <HeroCarousel
          autoplay
          dots
          effect="fade"
          autoplaySpeed={5000}
          pauseOnHover={false}
        >
          {/* Slide 1 - Diamond Manufacturing */}
          <div>
            <HeroSlide
              style={{
                backgroundImage: "url(/images/high-quality/diamond_workshop_hq.jpg)",
              }}
            >
              <HeroContent>
                <div style={{ marginBottom: "20px" }}>
                  <RamavatargemsLogo width={150} />
                </div>
                <Title
                  level={1}
                  style={{
                    color: "#ffffff",
                    fontSize: "4rem",
                    fontWeight: 900,
                    marginBottom: "20px",
                    textShadow: "0 4px 20px rgba(0, 0, 0, 0.8)",
                    letterSpacing: "2px",
                    fontFamily: "'Playfair Display', serif",
                  }}
                >
                  Premium{" "}
                  <span style={{
                    color: "#d4af37",
                    textShadow: "0 0 30px rgba(212, 175, 55, 0.8)"
                  }}>
                    Craftsmanship
                  </span>
                </Title>
                <Title
                  level={3}
                  style={{
                    color: "#ffffff",
                    fontSize: "1.8rem",
                    fontWeight: 400,
                    marginBottom: "30px",
                    textShadow: "0 2px 10px rgba(0, 0, 0, 0.8)",
                  }}
                >
                  Since 1982
                </Title>
                <Text
                  style={{
                    display: "block",
                    fontSize: "1.2rem",
                    color: "#ffffff",
                    marginBottom: "40px",
                    maxWidth: "700px",
                    margin: "0 auto 40px",
                    lineHeight: 1.7,
                    textShadow: "0 2px 10px rgba(0, 0, 0, 0.8)",
                  }}
                >
                  Where artistry meets precision. Our master craftsmen transform
                  rough diamonds into extraordinary works of art that capture light and imagination.
                </Text>
                <Space size="large" wrap>
                  <AntButton
                    type="primary"
                    size="large"
                    style={{
                      background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 50%, #d4af37 100%)",
                      border: "none",
                      color: "#000000",
                      fontSize: "1.1rem",
                      height: "auto",
                      padding: "15px 35px",
                      borderRadius: "30px",
                      fontWeight: 600,
                      boxShadow: "0 10px 30px rgba(212, 175, 55, 0.4)",
                    }}
                  >
                    <ToolOutlined style={{ marginRight: 8 }} />
                    Explore Our Process
                  </AntButton>
                  <AntButton
                    size="large"
                    style={{
                      borderColor: "#ffffff",
                      color: "#ffffff",
                      fontSize: "1.1rem",
                      height: "auto",
                      padding: "15px 35px",
                      borderRadius: "30px",
                      background: "rgba(255, 255, 255, 0.1)",
                      backdropFilter: "blur(10px)",
                      fontWeight: 600,
                    }}
                  >
                    <PlayCircleOutlined style={{ marginRight: 8 }} />
                    Watch Our Story
                  </AntButton>
                </Space>
              </HeroContent>
            </HeroSlide>
          </div>

          {/* Slide 2 - Quality Excellence */}
          <div>
            <HeroSlide
              style={{
                backgroundImage: "url(/images/jewelry_pieces/diamond_ring_making.jpg)",
              }}
            >
              <HeroContent>
                <Title
                  level={1}
                  style={{
                    color: "#ffffff",
                    fontSize: "4rem",
                    fontWeight: 900,
                    marginBottom: "20px",
                    textShadow: "0 4px 20px rgba(0, 0, 0, 0.8)",
                    letterSpacing: "2px",
                    fontFamily: "'Playfair Display', serif",
                  }}
                >
                  Uncompromising{" "}
                  <span style={{
                    color: "#d4af37",
                    textShadow: "0 0 30px rgba(212, 175, 55, 0.8)"
                  }}>
                    Quality
                  </span>
                </Title>
                <Text
                  style={{
                    display: "block",
                    fontSize: "1.2rem",
                    color: "#ffffff",
                    marginBottom: "40px",
                    maxWidth: "700px",
                    margin: "0 auto 40px",
                    lineHeight: 1.7,
                    textShadow: "0 2px 10px rgba(0, 0, 0, 0.8)",
                  }}
                >
                  Every diamond tells a story of perfection. Our rigorous quality standards
                  ensure that only the finest gems bear the Ramavatargems signature.
                </Text>
                <Space size="large" wrap>
                  <AntButton
                    type="primary"
                    size="large"
                    style={{
                      background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 50%, #d4af37 100%)",
                      border: "none",
                      color: "#000000",
                      fontSize: "1.1rem",
                      height: "auto",
                      padding: "15px 35px",
                      borderRadius: "30px",
                      fontWeight: 600,
                      boxShadow: "0 10px 30px rgba(212, 175, 55, 0.4)",
                    }}
                  >
                    <CrownOutlined style={{ marginRight: 8 }} />
                    View Collection
                  </AntButton>
                  <AntButton
                    size="large"
                    style={{
                      borderColor: "#ffffff",
                      color: "#ffffff",
                      fontSize: "1.1rem",
                      height: "auto",
                      padding: "15px 35px",
                      borderRadius: "30px",
                      background: "rgba(255, 255, 255, 0.1)",
                      backdropFilter: "blur(10px)",
                      fontWeight: 600,
                    }}
                  >
                    <PhoneOutlined style={{ marginRight: 8 }} />
                    Contact Us
                  </AntButton>
                </Space>
              </HeroContent>
            </HeroSlide>
          </div>
        </HeroCarousel>
      </HeroSection>

      {/* About Section */}
      <Section style={{ background: "var(--color-surface)" }}>
        <Container>
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <div style={{ animation: `${fadeInUp} 1s ease-out` }}>
                <Badge.Ribbon text="Since 1982" color="#d4af37">
                  <Title
                    level={2}
                    style={{
                      color: "var(--color-text-primary)",
                      fontSize: "3rem",
                      fontWeight: 900,
                      marginBottom: "30px",
                      fontFamily: "'Playfair Display', serif",
                    }}
                  >
                    Crafting{" "}
                    <span style={{ color: "#d4af37" }}>Excellence</span>
                  </Title>
                </Badge.Ribbon>
                <Paragraph
                  style={{
                    fontSize: "1.2rem",
                    color: "var(--color-text-secondary)",
                    lineHeight: 1.8,
                    marginBottom: "25px",
                  }}
                >
                  For over four decades, Ramavatargems has been at the forefront of
                  diamond jewelry manufacturing, combining traditional craftsmanship
                  with cutting-edge technology to create pieces that transcend time.
                </Paragraph>
                <Paragraph
                  style={{
                    fontSize: "1.1rem",
                    color: "var(--color-text-secondary)",
                    lineHeight: 1.7,
                    marginBottom: "30px",
                  }}
                >
                  Our master artisans bring decades of experience to every creation,
                  ensuring that each piece meets our uncompromising standards for
                  brilliance, clarity, and craftsmanship.
                </Paragraph>
                <Space size="large" wrap>
                  <AntButton
                    type="primary"
                    size="large"
                    style={{
                      background: "#d4af37",
                      borderColor: "#d4af37",
                      fontSize: "1.1rem",
                      height: "auto",
                      padding: "12px 30px",
                      borderRadius: "25px",
                      fontWeight: 600,
                    }}
                  >
                    <HistoryOutlined style={{ marginRight: 8 }} />
                    Our Story
                  </AntButton>
                  <AntButton
                    size="large"
                    style={{
                      borderColor: "#d4af37",
                      color: "#d4af37",
                      fontSize: "1.1rem",
                      height: "auto",
                      padding: "12px 30px",
                      borderRadius: "25px",
                      fontWeight: 600,
                    }}
                  >
                    <TrophyOutlined style={{ marginRight: 8 }} />
                    Awards & Recognition
                  </AntButton>
                </Space>
              </div>
            </Col>
            <Col xs={24} lg={12}>
              <div style={{ textAlign: "center" }}>
                <Image
                  src="/images/manufacturing/diamond_cutting_1.jpg"
                  alt="Diamond Manufacturing Process"
                  style={{
                    borderRadius: "20px",
                    boxShadow: "0 20px 60px rgba(0,0,0,0.15)",
                    maxWidth: "100%",
                    height: "auto",
                  }}
                  preview={false}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Statistics Section */}
      <Section style={{ background: "var(--color-background)" }}>
        <Container>
          <Title
            level={2}
            style={{
              textAlign: "center",
              color: "var(--color-text-primary)",
              fontSize: "3rem",
              fontWeight: 900,
              marginBottom: "60px",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            Our <span style={{ color: "#d4af37" }}>Legacy</span> in Numbers
          </Title>
          <Row gutter={[32, 32]}>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--color-card)",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  padding: "20px",
                  height: "100%",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 3s ease-in-out infinite` }}>
                  <HistoryOutlined
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "20px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Statistic
                  title={
                    <span style={{
                      color: "var(--color-text-primary)",
                      fontSize: "1.1rem",
                      fontWeight: 600
                    }}>
                      Years of Excellence
                    </span>
                  }
                  value="40+"
                  valueStyle={{
                    fontSize: "3rem",
                    fontWeight: 900,
                    color: "#d4af37",
                    textShadow: "0 2px 4px rgba(212, 175, 55, 0.3)",
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--color-card)",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  padding: "20px",
                  height: "100%",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 3s ease-in-out infinite 0.5s` }}>
                  <TeamOutlined
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "20px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Statistic
                  title={
                    <span style={{
                      color: "var(--color-text-primary)",
                      fontSize: "1.1rem",
                      fontWeight: 600
                    }}>
                      Master Craftsmen
                    </span>
                  }
                  value="50+"
                  valueStyle={{
                    fontSize: "3rem",
                    fontWeight: 900,
                    color: "#d4af37",
                    textShadow: "0 2px 4px rgba(212, 175, 55, 0.3)",
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--color-card)",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  padding: "20px",
                  height: "100%",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 3s ease-in-out infinite 1s` }}>
                  <GlobalOutlined
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "20px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Statistic
                  title={
                    <span style={{
                      color: "var(--color-text-primary)",
                      fontSize: "1.1rem",
                      fontWeight: 600
                    }}>
                      Countries Served
                    </span>
                  }
                  value="25+"
                  valueStyle={{
                    fontSize: "3rem",
                    fontWeight: 900,
                    color: "#d4af37",
                    textShadow: "0 2px 4px rgba(212, 175, 55, 0.3)",
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--color-card)",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  padding: "20px",
                  height: "100%",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 3s ease-in-out infinite 1.5s` }}>
                  <StarFilled
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "20px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Statistic
                  title={
                    <span style={{
                      color: "var(--color-text-primary)",
                      fontSize: "1.1rem",
                      fontWeight: 600
                    }}>
                      Satisfied Clients
                    </span>
                  }
                  value="10,000+"
                  valueStyle={{
                    fontSize: "3rem",
                    fontWeight: 900,
                    color: "#d4af37",
                    textShadow: "0 2px 4px rgba(212, 175, 55, 0.3)",
                  }}
                />
              </Card>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Services Section */}
      <Section style={{ background: "var(--color-surface)" }}>
        <Container>
          <Title
            level={2}
            style={{
              textAlign: "center",
              color: "var(--color-text-primary)",
              fontSize: "3rem",
              fontWeight: 900,
              marginBottom: "20px",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            Our <span style={{ color: "#d4af37" }}>Expertise</span>
          </Title>
          <Text
            style={{
              display: "block",
              textAlign: "center",
              fontSize: "1.2rem",
              color: "var(--color-text-secondary)",
              marginBottom: "60px",
              maxWidth: "600px",
              margin: "0 auto 60px",
            }}
          >
            Comprehensive diamond jewelry services backed by decades of experience
          </Text>
          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <Card
                style={{
                  height: "100%",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  background: "var(--color-card)",
                  padding: "30px 20px",
                  textAlign: "center",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 4s ease-in-out infinite` }}>
                  <DiamondOutlined
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "25px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Title
                  level={4}
                  style={{
                    color: "var(--color-text-primary)",
                    marginBottom: "20px",
                    fontSize: "1.4rem",
                    fontWeight: 700
                  }}
                >
                  Diamond Manufacturing
                </Title>
                <Paragraph
                  style={{
                    color: "var(--color-text-secondary)",
                    fontSize: "1rem",
                    lineHeight: 1.6
                  }}
                >
                  Expert diamond cutting, polishing, and setting services with precision
                  craftsmanship that brings out the natural beauty of each stone.
                </Paragraph>
                <Rate disabled defaultValue={5} style={{ color: "#d4af37", fontSize: "14px" }} />
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card
                style={{
                  height: "100%",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  background: "var(--color-card)",
                  padding: "30px 20px",
                  textAlign: "center",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 4s ease-in-out infinite 0.5s` }}>
                  <SafetyCertificateOutlined
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "25px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Title
                  level={4}
                  style={{
                    color: "var(--color-text-primary)",
                    marginBottom: "20px",
                    fontSize: "1.4rem",
                    fontWeight: 700
                  }}
                >
                  Quality Assurance
                </Title>
                <Paragraph
                  style={{
                    color: "var(--color-text-secondary)",
                    fontSize: "1rem",
                    lineHeight: 1.6
                  }}
                >
                  Rigorous quality control processes ensure every piece meets our
                  exceptional standards for brilliance, clarity, and craftsmanship.
                </Paragraph>
                <Rate disabled defaultValue={5} style={{ color: "#d4af37", fontSize: "14px" }} />
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card
                style={{
                  height: "100%",
                  borderRadius: "20px",
                  boxShadow: "var(--shadow-lg)",
                  border: "1px solid var(--color-border-light)",
                  background: "var(--color-card)",
                  padding: "30px 20px",
                  textAlign: "center",
                  transition: "all 0.3s ease",
                }}
                hoverable
              >
                <div style={{ animation: `${float} 4s ease-in-out infinite 1s` }}>
                  <CrownOutlined
                    style={{
                      fontSize: "4rem",
                      color: "#d4af37",
                      marginBottom: "25px",
                      filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
                    }}
                  />
                </div>
                <Title
                  level={4}
                  style={{
                    color: "var(--color-text-primary)",
                    marginBottom: "20px",
                    fontSize: "1.4rem",
                    fontWeight: 700
                  }}
                >
                  Custom Design
                </Title>
                <Paragraph
                  style={{
                    color: "var(--color-text-secondary)",
                    fontSize: "1rem",
                    lineHeight: 1.6
                  }}
                >
                  Bespoke jewelry design services that transform your vision into
                  stunning reality with personalized attention to every detail.
                </Paragraph>
                <Rate disabled defaultValue={5} style={{ color: "#d4af37", fontSize: "14px" }} />
              </Card>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Featured Collection */}
      <Section style={{ background: "var(--color-background)" }}>
        <Container>
          <Title
            level={2}
            style={{
              textAlign: "center",
              color: "var(--color-text-primary)",
              fontSize: "3rem",
              fontWeight: 900,
              marginBottom: "20px",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            Featured <span style={{ color: "#d4af37" }}>Collection</span>
          </Title>
          <Text
            style={{
              display: "block",
              textAlign: "center",
              fontSize: "1.2rem",
              color: "var(--color-text-secondary)",
              marginBottom: "60px",
              maxWidth: "600px",
              margin: "0 auto 60px",
            }}
          >
            Discover our latest masterpieces that showcase the pinnacle of diamond craftsmanship
          </Text>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} md={8}>
              <Card
                style={{
                  borderRadius: "20px",
                  overflow: "hidden",
                  boxShadow: "var(--shadow-lg)",
                  border: "none",
                  background: "var(--color-card)",
                }}
                hoverable
                cover={
                  <div style={{ height: "250px", overflow: "hidden" }}>
                    <Image
                      src="/images/jewelry_pieces/diamond_ring_1.jpg"
                      alt="Diamond Ring Collection"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        transition: "transform 0.3s ease"
                      }}
                      preview={false}
                    />
                  </div>
                }
              >
                <div style={{ padding: "10px" }}>
                  <Title level={5} style={{ color: "var(--color-text-primary)", marginBottom: "10px" }}>
                    Diamond Rings
                  </Title>
                  <Text style={{ color: "var(--color-text-secondary)" }}>
                    Exquisite engagement and wedding rings
                  </Text>
                  <div style={{ marginTop: "15px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Rate disabled defaultValue={5} style={{ fontSize: "12px", color: "#d4af37" }} />
                    <AntButton type="link" style={{ color: "#d4af37", padding: 0 }}>
                      View Collection <ArrowRightOutlined />
                    </AntButton>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Card
                style={{
                  borderRadius: "20px",
                  overflow: "hidden",
                  boxShadow: "var(--shadow-lg)",
                  border: "none",
                  background: "var(--color-card)",
                }}
                hoverable
                cover={
                  <div style={{ height: "250px", overflow: "hidden" }}>
                    <Image
                      src="/images/jewelry_pieces/diamond_necklace_1.jpg"
                      alt="Diamond Necklace Collection"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        transition: "transform 0.3s ease"
                      }}
                      preview={false}
                    />
                  </div>
                }
              >
                <div style={{ padding: "10px" }}>
                  <Title level={5} style={{ color: "var(--color-text-primary)", marginBottom: "10px" }}>
                    Diamond Necklaces
                  </Title>
                  <Text style={{ color: "var(--color-text-secondary)" }}>
                    Elegant necklaces for special occasions
                  </Text>
                  <div style={{ marginTop: "15px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Rate disabled defaultValue={5} style={{ fontSize: "12px", color: "#d4af37" }} />
                    <AntButton type="link" style={{ color: "#d4af37", padding: 0 }}>
                      View Collection <ArrowRightOutlined />
                    </AntButton>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Card
                style={{
                  borderRadius: "20px",
                  overflow: "hidden",
                  boxShadow: "var(--shadow-lg)",
                  border: "none",
                  background: "var(--color-card)",
                }}
                hoverable
                cover={
                  <div style={{ height: "250px", overflow: "hidden" }}>
                    <Image
                      src="/images/jewelry_pieces/diamond_earrings_1.jpg"
                      alt="Diamond Earrings Collection"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        transition: "transform 0.3s ease"
                      }}
                      preview={false}
                    />
                  </div>
                }
              >
                <div style={{ padding: "10px" }}>
                  <Title level={5} style={{ color: "var(--color-text-primary)", marginBottom: "10px" }}>
                    Diamond Earrings
                  </Title>
                  <Text style={{ color: "var(--color-text-secondary)" }}>
                    Stunning earrings that capture light beautifully
                  </Text>
                  <div style={{ marginTop: "15px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Rate disabled defaultValue={5} style={{ fontSize: "12px", color: "#d4af37" }} />
                    <AntButton type="link" style={{ color: "#d4af37", padding: 0 }}>
                      View Collection <ArrowRightOutlined />
                    </AntButton>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Call to Action */}
      <Section
        style={{
          background: "linear-gradient(135deg, #1a237e 0%, #000051 100%)",
          color: "white",
          position: "relative",
          overflow: "hidden"
        }}
      >
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundImage: "url(/images/high-quality/diamond_workshop_hq.jpg)",
            backgroundSize: "cover",
            backgroundPosition: "center",
            opacity: 0.1,
            zIndex: 1,
          }}
        />
        <Container style={{ textAlign: "center", position: "relative", zIndex: 2 }}>
          <Title
            level={2}
            style={{
              color: "#ffffff",
              fontSize: "3rem",
              fontWeight: 900,
              marginBottom: "20px",
              fontFamily: "'Playfair Display', serif",
              textShadow: "0 4px 20px rgba(0, 0, 0, 0.8)",
            }}
          >
            Ready to Create Something{" "}
            <span style={{ color: "#d4af37" }}>Extraordinary</span>?
          </Title>
          <Paragraph
            style={{
              color: "#ffffff",
              fontSize: "1.3rem",
              marginBottom: "40px",
              maxWidth: "700px",
              margin: "0 auto 40px",
              lineHeight: 1.7,
              textShadow: "0 2px 10px rgba(0, 0, 0, 0.8)",
            }}
          >
            Let our master craftsmen bring your diamond jewelry dreams to life with
            unparalleled expertise, precision, and attention to every sparkling detail.
          </Paragraph>
          <Space size="large" wrap>
            <AntButton
              type="primary"
              size="large"
              style={{
                background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 50%, #d4af37 100%)",
                border: "none",
                color: "#000000",
                fontSize: "1.2rem",
                height: "auto",
                padding: "18px 40px",
                borderRadius: "35px",
                fontWeight: 700,
                boxShadow: "0 15px 40px rgba(212, 175, 55, 0.4)",
                textTransform: "uppercase",
                letterSpacing: "1px",
              }}
            >
              <ShoppingOutlined style={{ marginRight: 10 }} />
              Start Your Journey
            </AntButton>
            <AntButton
              size="large"
              style={{
                borderColor: "#ffffff",
                color: "#ffffff",
                fontSize: "1.2rem",
                height: "auto",
                padding: "18px 40px",
                borderRadius: "35px",
                background: "rgba(255, 255, 255, 0.1)",
                backdropFilter: "blur(10px)",
                fontWeight: 700,
                textTransform: "uppercase",
                letterSpacing: "1px",
              }}
            >
              <PhoneOutlined style={{ marginRight: 10 }} />
              Contact Expert
            </AntButton>
          </Space>
          <div style={{ marginTop: "40px" }}>
            <Text style={{ color: "#ffffff", fontSize: "1rem", opacity: 0.9 }}>
              <EnvironmentOutlined style={{ marginRight: 8 }} />
              Visit our showroom or schedule a consultation
            </Text>
          </div>
        </Container>
      </Section>
    </PageContainer>
  );
};

export default Home;
