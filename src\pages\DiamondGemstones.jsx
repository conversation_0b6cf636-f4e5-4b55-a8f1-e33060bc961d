import React from "react";
import {
  Typography,
  Row,
  Col,
  Card,
  Tag,
  Space,
  Divider,
} from "antd";
import {
  DiamondOutlined,
  StarOutlined,
  SafetyCertificateOutlined,
  CrownOutlined,
} from "@ant-design/icons";
import styled from "styled-components";

const { Title, Text, Paragraph } = Typography;

// Styled Components
const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  padding: 80px 0;
`;

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
`;

const DiamondCard = styled(Card)`
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(232, 244, 253, 0.2) !important;
  border-radius: 20px !important;
  backdrop-filter: blur(20px);
  transition: all 0.4s ease !important;
  height: 100%;
  
  &:hover {
    transform: translateY(-8px);
    border-color: rgba(232, 244, 253, 0.5) !important;
    box-shadow: 0 20px 40px rgba(232, 244, 253, 0.2) !important;
  }
`;

const DiamondGemstones = () => {
  const diamondTypes = [
    {
      name: "Round Brilliant",
      description: "The most popular diamond cut, featuring 58 facets for maximum brilliance and fire.",
      image: "/images/diamonds/round_brilliant.jpg",
      characteristics: ["Maximum Brilliance", "Timeless Appeal", "Versatile Setting Options"],
      icon: <DiamondOutlined />
    },
    {
      name: "Princess Cut",
      description: "Square-shaped diamond with exceptional sparkle and contemporary appeal.",
      image: "/images/diamonds/princess_cut.jpg",
      characteristics: ["Modern Elegance", "Sharp Corners", "Brilliant Faceting"],
      icon: <CrownOutlined />
    },
    {
      name: "Emerald Cut",
      description: "Rectangular step-cut diamond showcasing clarity and sophisticated elegance.",
      image: "/images/diamonds/emerald_cut.jpg",
      characteristics: ["Vintage Charm", "Hall of Mirrors Effect", "Elegant Lines"],
      icon: <StarOutlined />
    },
    {
      name: "Cushion Cut",
      description: "Pillow-shaped diamond combining vintage charm with brilliant sparkle.",
      image: "/images/diamonds/cushion_cut.jpg",
      characteristics: ["Romantic Appeal", "Soft Corners", "Vintage Inspired"],
      icon: <DiamondOutlined />
    }
  ];

  const diamondGrades = [
    {
      category: "Cut",
      description: "Determines how well the diamond reflects light",
      grades: ["Excellent", "Very Good", "Good", "Fair", "Poor"],
      importance: "Most important factor for brilliance"
    },
    {
      category: "Clarity",
      description: "Measures internal and external flaws",
      grades: ["FL", "IF", "VVS1", "VVS2", "VS1", "VS2", "SI1", "SI2"],
      importance: "Affects diamond's transparency"
    },
    {
      category: "Color",
      description: "Grades the absence of color in diamonds",
      grades: ["D", "E", "F", "G", "H", "I", "J", "K", "L", "M"],
      importance: "Colorless diamonds are most valuable"
    },
    {
      category: "Carat",
      description: "Measures the diamond's weight",
      grades: ["0.25ct", "0.50ct", "1.00ct", "2.00ct", "3.00ct+"],
      importance: "Larger diamonds are exponentially rarer"
    }
  ];

  return (
    <PageContainer>
      <Container>
        {/* Header Section */}
        <div style={{ textAlign: "center", marginBottom: "80px" }}>
          <Title
            level={1}
            style={{
              color: "#ffffff",
              fontSize: "4rem",
              fontWeight: 900,
              marginBottom: "20px",
              fontFamily: "'Playfair Display', serif",
              textShadow: "0 4px 20px rgba(0, 0, 0, 0.8)",
            }}
          >
            Diamond{" "}
            <span style={{ 
              color: "#E8F4FD",
              textShadow: "0 0 40px rgba(232, 244, 253, 0.9)",
              filter: "drop-shadow(0 0 15px rgba(255, 255, 255, 0.6))"
            }}>
              Excellence
            </span>
          </Title>
          <Text
            style={{
              color: "rgba(255, 255, 255, 0.9)",
              fontSize: "1.4rem",
              maxWidth: "800px",
              margin: "0 auto",
              display: "block",
              lineHeight: 1.7,
            }}
          >
            Discover our carefully curated selection of premium diamonds, 
            each certified for exceptional quality and brilliance.
          </Text>
        </div>

        {/* Diamond Types Section */}
        <div style={{ marginBottom: "100px" }}>
          <Title
            level={2}
            style={{
              color: "#ffffff",
              fontSize: "3rem",
              fontWeight: 900,
              marginBottom: "50px",
              textAlign: "center",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            Diamond{" "}
            <span style={{ color: "#E8F4FD" }}>Cuts</span>{" "}
            We Specialize In
          </Title>

          <Row gutter={[40, 40]}>
            {diamondTypes.map((diamond, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <DiamondCard>
                  <div style={{ position: "relative", marginBottom: "20px" }}>
                    <img
                      src={diamond.image}
                      alt={diamond.name}
                      style={{
                        width: "100%",
                        height: "200px",
                        objectFit: "cover",
                        borderRadius: "15px",
                      }}
                    />
                    <div
                      style={{
                        position: "absolute",
                        top: "15px",
                        right: "15px",
                        background: "rgba(232, 244, 253, 0.95)",
                        color: "#1a237e",
                        padding: "10px",
                        borderRadius: "50%",
                        fontSize: "1.2rem",
                      }}
                    >
                      {diamond.icon}
                    </div>
                  </div>
                  
                  <div style={{ padding: "0 20px 20px" }}>
                    <Title
                      level={4}
                      style={{
                        color: "#ffffff",
                        marginBottom: "12px",
                        fontSize: "1.3rem",
                        fontWeight: "700",
                      }}
                    >
                      {diamond.name}
                    </Title>
                    <Paragraph
                      style={{
                        color: "rgba(255, 255, 255, 0.8)",
                        fontSize: "1rem",
                        lineHeight: 1.6,
                        marginBottom: "16px",
                      }}
                    >
                      {diamond.description}
                    </Paragraph>
                    <Space direction="vertical" size="small" style={{ width: "100%" }}>
                      {diamond.characteristics.map((char, idx) => (
                        <Tag
                          key={idx}
                          style={{
                            background: "rgba(232, 244, 253, 0.1)",
                            border: "1px solid rgba(232, 244, 253, 0.3)",
                            color: "#E8F4FD",
                            borderRadius: "15px",
                            padding: "4px 12px",
                          }}
                        >
                          {char}
                        </Tag>
                      ))}
                    </Space>
                  </div>
                </DiamondCard>
              </Col>
            ))}
          </Row>
        </div>

        {/* Diamond Grading Section */}
        <div style={{ marginBottom: "80px" }}>
          <Title
            level={2}
            style={{
              color: "#ffffff",
              fontSize: "3rem",
              fontWeight: 900,
              marginBottom: "20px",
              textAlign: "center",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            The 4 C's of{" "}
            <span style={{ color: "#E8F4FD" }}>Diamond</span>{" "}
            Quality
          </Title>
          <Text
            style={{
              color: "rgba(255, 255, 255, 0.8)",
              fontSize: "1.2rem",
              textAlign: "center",
              marginBottom: "50px",
              display: "block",
            }}
          >
            Understanding diamond quality through Cut, Clarity, Color, and Carat weight
          </Text>

          <Row gutter={[30, 30]}>
            {diamondGrades.map((grade, index) => (
              <Col xs={24} md={12} lg={6} key={index}>
                <DiamondCard>
                  <div style={{ padding: "30px 20px" }}>
                    <div
                      style={{
                        textAlign: "center",
                        marginBottom: "20px",
                      }}
                    >
                      <div
                        style={{
                          width: "60px",
                          height: "60px",
                          background: "linear-gradient(135deg, #E8F4FD 0%, #FFFFFF 100%)",
                          borderRadius: "50%",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          margin: "0 auto 15px",
                          fontSize: "1.5rem",
                          color: "#1a237e",
                          fontWeight: "bold",
                        }}
                      >
                        {grade.category[0]}
                      </div>
                      <Title
                        level={4}
                        style={{
                          color: "#ffffff",
                          marginBottom: "8px",
                          fontSize: "1.4rem",
                          fontWeight: "700",
                        }}
                      >
                        {grade.category}
                      </Title>
                    </div>
                    
                    <Paragraph
                      style={{
                        color: "rgba(255, 255, 255, 0.8)",
                        fontSize: "1rem",
                        lineHeight: 1.6,
                        marginBottom: "16px",
                        textAlign: "center",
                      }}
                    >
                      {grade.description}
                    </Paragraph>
                    
                    <Divider style={{ borderColor: "rgba(232, 244, 253, 0.2)" }} />
                    
                    <div style={{ textAlign: "center" }}>
                      <Text
                        style={{
                          color: "#E8F4FD",
                          fontSize: "0.9rem",
                          fontWeight: "600",
                          display: "block",
                          marginBottom: "8px",
                        }}
                      >
                        {grade.importance}
                      </Text>
                      <Space wrap>
                        {grade.grades.slice(0, 4).map((gradeLevel, idx) => (
                          <Tag
                            key={idx}
                            style={{
                              background: "rgba(26, 35, 126, 0.3)",
                              border: "1px solid rgba(232, 244, 253, 0.3)",
                              color: "rgba(255, 255, 255, 0.9)",
                              borderRadius: "10px",
                              fontSize: "0.8rem",
                            }}
                          >
                            {gradeLevel}
                          </Tag>
                        ))}
                      </Space>
                    </div>
                  </div>
                </DiamondCard>
              </Col>
            ))}
          </Row>
        </div>

        {/* Certification Section */}
        <div style={{ textAlign: "center" }}>
          <DiamondCard style={{ maxWidth: "800px", margin: "0 auto" }}>
            <div style={{ padding: "40px" }}>
              <SafetyCertificateOutlined
                style={{
                  fontSize: "3rem",
                  color: "#E8F4FD",
                  marginBottom: "20px",
                }}
              />
              <Title
                level={3}
                style={{
                  color: "#ffffff",
                  marginBottom: "20px",
                  fontFamily: "'Playfair Display', serif",
                }}
              >
                Certified Diamond Excellence
              </Title>
              <Paragraph
                style={{
                  color: "rgba(255, 255, 255, 0.8)",
                  fontSize: "1.1rem",
                  lineHeight: 1.7,
                  marginBottom: "20px",
                }}
              >
                All our diamonds come with internationally recognized certifications from 
                prestigious institutions like GIA (Gemological Institute of America) and 
                IGI (International Gemological Institute), ensuring authenticity, quality, 
                and ethical sourcing.
              </Paragraph>
              <Space size="large">
                <Tag
                  style={{
                    background: "rgba(232, 244, 253, 0.1)",
                    border: "1px solid rgba(232, 244, 253, 0.3)",
                    color: "#E8F4FD",
                    borderRadius: "20px",
                    padding: "8px 20px",
                    fontSize: "1rem",
                  }}
                >
                  GIA Certified
                </Tag>
                <Tag
                  style={{
                    background: "rgba(232, 244, 253, 0.1)",
                    border: "1px solid rgba(232, 244, 253, 0.3)",
                    color: "#E8F4FD",
                    borderRadius: "20px",
                    padding: "8px 20px",
                    fontSize: "1rem",
                  }}
                >
                  IGI Certified
                </Tag>
                <Tag
                  style={{
                    background: "rgba(232, 244, 253, 0.1)",
                    border: "1px solid rgba(232, 244, 253, 0.3)",
                    color: "#E8F4FD",
                    borderRadius: "20px",
                    padding: "8px 20px",
                    fontSize: "1rem",
                  }}
                >
                  Ethically Sourced
                </Tag>
              </Space>
            </div>
          </DiamondCard>
        </div>
      </Container>
    </PageContainer>
  );
};

export default DiamondGemstones;
