import React, { useEffect } from "react";
import { Typography, Row, Col, Card, Statistic, Space } from "antd";
import {
  CheckCircleFilled,
  HistoryOutlined,
  TeamOutlined,
  GlobalOutlined,
  DashboardOutlined,
  ToolOutlined,
  ArrowRightOutlined,
} from "@ant-design/icons";
import Autoplay from "embla-carousel-autoplay";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "../components/ui/carousel";
import { Button } from "../components/ui/button";
import RamavatargemsLogo from "../components/logo/RamavatargemsLogo";

const { Title, Text, Paragraph } = Typography;

const HomeShadcn = () => {
  const plugin = React.useRef(
    Autoplay({ delay: 5000, stopOnInteraction: true })
  );

  const carouselSlides = [
    {
      id: 1,
      backgroundImage: "/images/high-quality/diamond_workshop_hq.jpg",
      title: "Crafting Timeless",
      highlightText: "Diamond",
      subtitle: "Elegance",
      description: "With Precision & Passion Since 1982",
      primaryButton: "Explore Our Craftsmanship",
      secondaryButton: "View Our Diamond Process",
    },
    {
      id: 2,
      backgroundImage: "/images/high-quality/diamond_cutting_hq.jpg",
      title: "Precision",
      highlightText: "Diamond",
      subtitle: "Craftsmanship",
      description: "Master artisans transforming precious diamonds into timeless masterpieces with unparalleled skill and dedication.",
      primaryButton: "Discover Our Expertise",
      secondaryButton: "View Diamond Gallery",
    },
  ];

  return (
    <div className="min-h-screen bg-background text-foreground overflow-x-hidden">
      {/* Hero Carousel Section */}
      <section className="relative h-screen min-h-[500px] max-h-screen mt-20 overflow-hidden">
        <Carousel
          plugins={[plugin.current]}
          className="w-full h-full"
          onMouseEnter={plugin.current.stop}
          onMouseLeave={plugin.current.reset}
        >
          <CarouselContent className="h-full">
            {carouselSlides.map((slide) => (
              <CarouselItem key={slide.id} className="h-full">
                <div
                  className="relative h-full w-full bg-cover bg-center bg-no-repeat flex items-center justify-center"
                  style={{
                    backgroundImage: `url(${slide.backgroundImage})`,
                  }}
                >
                  {/* Overlay Gradients */}
                  <div className="absolute inset-0 bg-gradient-to-br from-black/85 via-blue-900/75 to-black/85 z-10" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent z-20" />
                  
                  {/* Content */}
                  <div className="relative z-30 max-w-4xl mx-auto px-6 text-center">
                    <div className="bg-black/40 backdrop-blur-xl rounded-3xl border border-white/30 p-8 md:p-12 shadow-2xl">
                      {/* Logo */}
                      <div className="mb-8 filter drop-shadow-2xl">
                        <RamavatargemsLogo width={150} />
                      </div>
                      
                      {/* Title */}
                      <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold font-playfair text-white mb-6 leading-tight">
                        {slide.title}{" "}
                        <span className="text-blue-200 animate-glow filter drop-shadow-lg">
                          {slide.highlightText}
                        </span>{" "}
                        {slide.subtitle}
                      </h1>
                      
                      {/* Description */}
                      <p className="text-lg md:text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                        {slide.description}
                      </p>
                      
                      {/* Buttons */}
                      <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <Button 
                          variant="diamond" 
                          size="xl"
                          className="min-w-[200px] font-bold uppercase tracking-wide"
                        >
                          <ToolOutlined className="mr-2 h-5 w-5" />
                          {slide.primaryButton}
                        </Button>
                        <Button 
                          variant="diamondOutline" 
                          size="xl"
                          className="min-w-[200px] font-semibold uppercase tracking-wide"
                        >
                          <ArrowRightOutlined className="mr-2 h-5 w-5" />
                          {slide.secondaryButton}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          
          {/* Navigation */}
          <CarouselPrevious className="left-8 h-12 w-12 bg-black/20 border-white/20 text-white hover:bg-blue-600/30 hover:border-blue-400 backdrop-blur-sm" />
          <CarouselNext className="right-8 h-12 w-12 bg-black/20 border-white/20 text-white hover:bg-blue-600/30 hover:border-blue-400 backdrop-blur-sm" />
        </Carousel>
      </section>

      {/* About Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 to-slate-800">
        <div className="max-w-7xl mx-auto px-6">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} md={12}>
              <Title
                level={2}
                className="text-4xl md:text-5xl font-bold font-playfair text-white mb-6"
              >
                Diamond{" "}
                <span className="text-blue-200 animate-glow">
                  Manufacturing
                </span>{" "}
                Excellence
              </Title>
              <Text className="text-xl text-blue-100 block mb-6">
                Diamond Jewelry Manufacturing Excellence Since 1982
              </Text>
              <Paragraph className="text-lg text-gray-300 mb-8 leading-relaxed">
                We are specialists in manufacturing premium diamond jewelry, combining
                age-old artistry with cutting-edge technology. Every piece is crafted with
                precision, ensuring brilliance and durability. Our master craftsmen possess
                decades of expertise in diamond setting, working with certified diamonds
                sourced through ethical channels.
              </Paragraph>
              <Space direction="vertical" size="middle" className="w-full">
                {[
                  "Bespoke diamond jewelry design and creation",
                  "Expert diamond setting and precision metalwork",
                  "Ethical sourcing of certified diamonds and precious metals"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircleFilled className="text-blue-400 text-xl mr-4" />
                    <Text className="text-white text-lg font-medium">
                      {feature}
                    </Text>
                  </div>
                ))}
              </Space>
            </Col>
            <Col xs={24} md={12}>
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="/images/high-quality/diamond_inspection_hq.jpg"
                  alt="Diamond jewelry manufacturing workshop"
                  className="w-full h-auto"
                />
                <div className="absolute top-6 right-6 bg-blue-600/90 backdrop-blur-sm text-white px-6 py-3 rounded-full font-bold text-lg shadow-lg">
                  Since 1982
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-br from-slate-800 to-slate-900">
        <div className="max-w-7xl mx-auto px-6">
          <Title
            level={2}
            className="text-center text-4xl md:text-5xl font-bold font-playfair text-white mb-16"
          >
            Diamond{" "}
            <span className="text-blue-200 animate-glow">
              Excellence
            </span>{" "}
            Milestones
          </Title>
          <Row gutter={[32, 32]}>
            {[
              { icon: <HistoryOutlined />, value: "40+", title: "Years of Diamond Excellence" },
              { icon: <TeamOutlined />, value: "25+", title: "Diamond Specialists" },
              { icon: <GlobalOutlined />, value: "15+", title: "Countries Served" },
              { icon: <DashboardOutlined />, value: "10,000+", title: "Diamond Pieces Created" },
            ].map((stat, index) => (
              <Col xs={24} sm={12} md={6} key={index}>
                <Card className="text-center bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-700/50 transition-all duration-300 transform hover:scale-105">
                  <div className="text-5xl text-blue-400 mb-4">
                    {stat.icon}
                  </div>
                  <Statistic
                    title={
                      <span className="text-white text-lg font-semibold">
                        {stat.title}
                      </span>
                    }
                    value={stat.value}
                    valueStyle={{
                      fontSize: "3rem",
                      fontWeight: 800,
                      color: "#60a5fa",
                      fontFamily: "'Playfair Display', serif",
                    }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>
    </div>
  );
};

export default HomeShadcn;
