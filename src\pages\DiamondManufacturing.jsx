import React from "react";
import {
  Typo<PERSON>,
  <PERSON>,
  Col,
  Card,
  Steps,
  Image,
  Space,
  But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "antd";
import {
  DesignServicesOutlined,
  DiamondOutlined,
  BuildOutlined,
  SettingOutlined,
  StarOutlined,
  SafetyCertificateOutlined,
} from "@ant-design/icons";
import styled from "styled-components";

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Styled Components
const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
`;

const HeroSection = styled.div`
  position: relative;
  height: 60vh;
  background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
    url("/images/jewelry_manufacturing/stone_setting.jpg");
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
`;

const Section = styled.section`
  padding: 80px 0;
  position: relative;
`;

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
`;

const ProcessCard = styled(Card)`
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(232, 244, 253, 0.2) !important;
  border-radius: 20px !important;
  backdrop-filter: blur(20px);
  transition: all 0.4s ease !important;
  height: 100%;
  
  &:hover {
    transform: translateY(-10px);
    border-color: rgba(232, 244, 253, 0.5) !important;
    box-shadow: 0 20px 40px rgba(232, 244, 253, 0.2) !important;
  }
`;

const DiamondManufacturing = () => {
  const manufacturingSteps = [
    {
      title: "Design Sketching",
      description: "Hand-drawn sketches and CAD designs bring concepts to life with precision and artistry.",
      image: "/images/jewelry_manufacturing/design.jpg",
      icon: <DesignServicesOutlined />,
      details: "Our master designers create detailed sketches and 3D CAD models, ensuring every angle and proportion is perfect before production begins."
    },
    {
      title: "Diamond Sourcing & Certification",
      description: "Ethically sourced, certified diamonds selected for optimal brilliance and quality.",
      image: "/images/diamonds/round_brilliant.jpg",
      icon: <DiamondOutlined />,
      details: "We source only certified diamonds from reputable suppliers, ensuring each stone meets our strict standards for cut, clarity, color, and carat weight."
    },
    {
      title: "Casting & Mold Preparation",
      description: "Precision casting creates the foundation for exceptional diamond jewelry pieces.",
      image: "/images/jewelry_manufacturing/casting.jpg",
      icon: <BuildOutlined />,
      details: "Using advanced casting techniques, we create precise metal frameworks that will securely hold and showcase each diamond to its fullest potential."
    },
    {
      title: "Diamond Setting",
      description: "Master craftsmen carefully set each diamond with precision and expertise.",
      image: "/images/jewelry_manufacturing/stone_setting.jpg",
      icon: <SettingOutlined />,
      details: "Our skilled artisans use traditional and modern setting techniques to ensure each diamond is securely placed while maximizing its brilliance and fire."
    },
    {
      title: "Polishing & Finishing",
      description: "Meticulous polishing brings out the ultimate brilliance in every piece.",
      image: "/images/jewelry_manufacturing/polishing.jpg",
      icon: <StarOutlined />,
      details: "Through careful polishing and finishing, we enhance the metal's luster and ensure the diamonds achieve maximum light reflection and sparkle."
    },
    {
      title: "Quality Control",
      description: "Rigorous inspection ensures every piece meets our exacting standards.",
      image: "/images/jewelry_manufacturing/quality_check.jpg",
      icon: <SafetyCertificateOutlined />,
      details: "Each finished piece undergoes comprehensive quality control, checking diamond security, metal integrity, and overall craftsmanship excellence."
    }
  ];

  return (
    <PageContainer>
      {/* Hero Section */}
      <HeroSection>
        <Container>
          <Title
            level={1}
            style={{
              color: "#ffffff",
              fontSize: "4rem",
              fontWeight: 900,
              marginBottom: "20px",
              textShadow: "0 4px 20px rgba(0, 0, 0, 0.8)",
              fontFamily: "'Playfair Display', serif",
            }}
          >
            Diamond Jewelry{" "}
            <span style={{ 
              color: "#E8F4FD",
              textShadow: "0 0 40px rgba(232, 244, 253, 0.9)",
              filter: "drop-shadow(0 0 15px rgba(255, 255, 255, 0.6))"
            }}>
              Manufacturing
            </span>{" "}
            Process
          </Title>
          <Text
            style={{
              color: "#ffffff",
              fontSize: "1.4rem",
              textShadow: "0 2px 10px rgba(0, 0, 0, 0.8)",
              maxWidth: "800px",
              margin: "0 auto",
              display: "block",
              lineHeight: 1.6,
            }}
          >
            Discover the meticulous craftsmanship behind every diamond jewelry piece, 
            from initial design to final quality inspection.
          </Text>
        </Container>
      </HeroSection>

      {/* Manufacturing Process Steps */}
      <Section style={{ background: "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)" }}>
        <Container>
          <div style={{ textAlign: "center", marginBottom: "80px" }}>
            <Title
              level={2}
              style={{
                color: "#ffffff",
                fontSize: "3.5rem",
                fontWeight: 900,
                marginBottom: "20px",
                fontFamily: "'Playfair Display', serif",
              }}
            >
              Our{" "}
              <span style={{ 
                color: "#E8F4FD",
                textShadow: "0 0 30px rgba(232, 244, 253, 0.8)"
              }}>
                Craftsmanship
              </span>{" "}
              Journey
            </Title>
            <Text
              style={{
                color: "rgba(255, 255, 255, 0.9)",
                fontSize: "1.3rem",
                maxWidth: "700px",
                margin: "0 auto",
                display: "block",
                lineHeight: 1.7,
              }}
            >
              Each step in our manufacturing process is executed with precision, 
              passion, and decades of expertise.
            </Text>
          </div>

          <Row gutter={[40, 40]}>
            {manufacturingSteps.map((step, index) => (
              <Col xs={24} md={12} lg={8} key={index}>
                <ProcessCard>
                  <div style={{ position: "relative", marginBottom: "20px" }}>
                    <Image
                      src={step.image}
                      alt={step.title}
                      style={{ 
                        borderRadius: "15px",
                        height: "250px",
                        objectFit: "cover",
                        width: "100%"
                      }}
                      preview={false}
                    />
                    <div
                      style={{
                        position: "absolute",
                        top: "15px",
                        left: "15px",
                        background: "rgba(232, 244, 253, 0.95)",
                        color: "#1a237e",
                        padding: "12px",
                        borderRadius: "50%",
                        fontSize: "1.5rem",
                        fontWeight: "bold",
                        boxShadow: "0 4px 15px rgba(232, 244, 253, 0.4)",
                      }}
                    >
                      {step.icon}
                    </div>
                    <div
                      style={{
                        position: "absolute",
                        top: "15px",
                        right: "15px",
                        background: "rgba(26, 35, 126, 0.9)",
                        color: "#E8F4FD",
                        padding: "8px 16px",
                        borderRadius: "20px",
                        fontSize: "0.9rem",
                        fontWeight: "600",
                      }}
                    >
                      Step {index + 1}
                    </div>
                  </div>
                  
                  <div style={{ padding: "0 20px 20px" }}>
                    <Title
                      level={4}
                      style={{
                        color: "#ffffff",
                        marginBottom: "12px",
                        fontSize: "1.3rem",
                        fontWeight: "700",
                      }}
                    >
                      {step.title}
                    </Title>
                    <Paragraph
                      style={{
                        color: "rgba(255, 255, 255, 0.8)",
                        fontSize: "1rem",
                        lineHeight: 1.6,
                        marginBottom: "16px",
                      }}
                    >
                      {step.description}
                    </Paragraph>
                    <Paragraph
                      style={{
                        color: "rgba(255, 255, 255, 0.7)",
                        fontSize: "0.9rem",
                        lineHeight: 1.5,
                        marginBottom: "20px",
                      }}
                    >
                      {step.details}
                    </Paragraph>
                  </div>
                </ProcessCard>
              </Col>
            ))}
          </Row>
        </Container>
      </Section>
    </PageContainer>
  );
};

export default DiamondManufacturing;
