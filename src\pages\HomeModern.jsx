import React from "react";
import { Typography, Row, Col, Card, Statistic, Space } from "antd";
import {
  CheckCircleFilled,
  HistoryOutlined,
  TeamOutlined,
  GlobalOutlined,
  DashboardOutlined,
} from "@ant-design/icons";
import DiamondCarousel from "../components/modern/DiamondCarousel";

const { Title, Text, Paragraph } = Typography;

const HomeModern = () => {
  return (
    <div className="min-h-screen bg-background text-foreground overflow-x-hidden">
      {/* Hero Carousel Section */}
      <section className="relative mt-20">
        <DiamondCarousel />
      </section>

      {/* About Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
        <div className="max-w-7xl mx-auto px-6">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} md={12}>
              <div className="animate-fade-in">
                <Title
                  level={2}
                  className="text-4xl md:text-5xl font-bold font-playfair text-white mb-6"
                >
                  Diamond{" "}
                  <span className="text-blue-300 animate-glow">
                    Manufacturing
                  </span>{" "}
                  Excellence
                </Title>
                <Text className="text-xl text-blue-100 block mb-6">
                  Diamond Jewelry Manufacturing Excellence Since 1982
                </Text>
                <Paragraph className="text-lg text-gray-300 mb-8 leading-relaxed">
                  We are specialists in manufacturing premium diamond jewelry, combining
                  age-old artistry with cutting-edge technology. Every piece is crafted with
                  precision, ensuring brilliance and durability. Our master craftsmen possess
                  decades of expertise in diamond setting, working with certified diamonds
                  sourced through ethical channels.
                </Paragraph>
                <Space direction="vertical" size="middle" className="w-full">
                  {[
                    "Bespoke diamond jewelry design and creation",
                    "Expert diamond setting and precision metalwork",
                    "Ethical sourcing of certified diamonds and precious metals"
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center group">
                      <CheckCircleFilled className="text-blue-400 text-xl mr-4 group-hover:scale-110 transition-transform duration-300" />
                      <Text className="text-white text-lg font-medium">
                        {feature}
                      </Text>
                    </div>
                  ))}
                </Space>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="relative rounded-2xl overflow-hidden shadow-2xl animate-fade-in">
                <img
                  src="/images/high-quality/diamond_inspection_hq.jpg"
                  alt="Diamond jewelry manufacturing workshop"
                  className="w-full h-auto transform hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute top-6 right-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full font-bold text-lg shadow-lg animate-float">
                  Since 1982
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
              </div>
            </Col>
          </Row>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-br from-slate-800 via-purple-900 to-slate-900">
        <div className="max-w-7xl mx-auto px-6">
          <Title
            level={2}
            className="text-center text-4xl md:text-5xl font-bold font-playfair text-white mb-16 animate-fade-in"
          >
            Diamond{" "}
            <span className="text-blue-300 animate-glow">
              Excellence
            </span>{" "}
            Milestones
          </Title>
          <Row gutter={[32, 32]}>
            {[
              { icon: <HistoryOutlined />, value: "40+", title: "Years of Diamond Excellence" },
              { icon: <TeamOutlined />, value: "25+", title: "Diamond Specialists" },
              { icon: <GlobalOutlined />, value: "15+", title: "Countries Served" },
              { icon: <DashboardOutlined />, value: "10,000+", title: "Diamond Pieces Created" },
            ].map((stat, index) => (
              <Col xs={24} sm={12} md={6} key={index}>
                <Card className="text-center bg-black/30 border-blue-500/20 backdrop-blur-sm hover:bg-black/40 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl group animate-fade-in">
                  <div className="text-5xl text-blue-400 mb-4 group-hover:scale-110 transition-transform duration-300">
                    {stat.icon}
                  </div>
                  <Statistic
                    title={
                      <span className="text-white text-lg font-semibold">
                        {stat.title}
                      </span>
                    }
                    value={stat.value}
                    valueStyle={{
                      fontSize: "3rem",
                      fontWeight: 800,
                      color: "#93c5fd",
                      fontFamily: "'Playfair Display', serif",
                    }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>
        
        <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
          <Title
            level={2}
            className="text-4xl md:text-6xl font-bold font-playfair text-white mb-6 animate-fade-in"
          >
            Ready to Create{" "}
            <span className="text-blue-300 animate-glow">
              Diamond
            </span>{" "}
            Magic?
          </Title>
          <Paragraph className="text-xl text-blue-100 mb-10 leading-relaxed animate-fade-in">
            Let our master craftsmen transform your vision into a stunning diamond jewelry masterpiece. 
            Experience the perfect blend of traditional artistry and modern precision.
          </Paragraph>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in">
            <button className="group relative px-10 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-bold text-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 min-w-[200px] overflow-hidden">
              <span className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              <span className="relative">Start Your Journey</span>
            </button>
            
            <button className="group relative px-10 py-4 border-2 border-blue-400 text-blue-400 bg-transparent hover:bg-blue-400 hover:text-white backdrop-blur-sm rounded-full font-semibold text-xl transition-all duration-300 min-w-[200px] overflow-hidden">
              <span className="absolute inset-0 bg-blue-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              <span className="relative">View Portfolio</span>
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomeModern;
