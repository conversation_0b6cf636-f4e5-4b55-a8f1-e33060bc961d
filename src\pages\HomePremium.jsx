import React from "react";
import {
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Carousel,
  Space,
  But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "antd";
import {
  ArrowRightOutlined,
  StarFilled,
  DiamondOutlined,
  CrownOutlined,
  FireOutlined,
} from "@ant-design/icons";
import styled from "styled-components";
import { useTheme } from "../context/ThemeContext";
import RamavatargemsLogo from "../components/logo/RamavatargemsLogo";

const { Title, Text, Paragraph } = Typography;

// Styled Components
const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
`;

const HeroSection = styled.div`
  position: relative;
  height: 100vh;
  overflow: hidden;
`;

const HeroSlide = styled.div`
  position: relative;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.7) 0%,
      rgba(26, 26, 46, 0.6) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
    z-index: 1;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1200px;
  padding: 0 40px;
`;

const PremiumCarousel = styled(Carousel)`
  .ant-carousel .ant-carousel-dots {
    bottom: 40px;

    li button {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: rgba(212, 175, 55, 0.5);
      border: 2px solid rgba(212, 175, 55, 0.8);
    }

    li.ant-carousel-dot-active button {
      background: #d4af37;
      box-shadow: 0 0 20px rgba(212, 175, 55, 0.8);
    }
  }
`;

const Section = styled.section`
  padding: 120px 0;
  position: relative;
`;

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
`;

const DiamondShowcase = styled.div`
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle at 30% 70%,
      rgba(212, 175, 55, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(212, 175, 55, 0.08) 0%,
      transparent 50%
    );
    z-index: 1;
  }
`;

const DiamondCard = styled(Card)`
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
  border-radius: 24px !important;
  backdrop-filter: blur(20px);
  transition: all 0.4s ease !important;
  overflow: hidden;

  &:hover {
    transform: translateY(-10px);
    border-color: rgba(212, 175, 55, 0.5) !important;
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2) !important;
  }

  .ant-card-body {
    padding: 0 !important;
  }
`;

const ImageContainer = styled.div`
  position: relative;
  height: 350px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
  }

  &:hover img {
    transform: scale(1.1);
  }

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.8) 0%,
      transparent 100%
    );
  }
`;

const CardContent = styled.div`
  padding: 30px;
  position: relative;
  z-index: 2;
`;

const HomePremium = () => {
  const { isDarkMode } = useTheme();

  const diamondCollections = [
    {
      title: "Brilliant Cut Diamonds",
      description: "The most popular and brilliant diamond cut, maximizing light reflection and sparkle",
      image: "/images/diamonds/round_brilliant.jpg",
      icon: <DiamondOutlined />,
    },
    {
      title: "Princess Cut Elegance",
      description: "Square-shaped diamonds with exceptional brilliance and modern appeal",
      image: "/images/diamonds/princess_cut.jpg",
      icon: <CrownOutlined />,
    },
    {
      title: "Emerald Cut Sophistication",
      description: "Rectangular diamonds with step-cut facets for elegant, vintage charm",
      image: "/images/diamonds/emerald_cut.jpg",
      icon: <StarFilled />,
    },
    {
      title: "Cushion Cut Romance",
      description: "Pillow-shaped diamonds combining vintage charm with brilliant sparkle",
      image: "/images/diamonds/cushion_cut.jpg",
      icon: <FireOutlined />,
    },
  ];

  return (
    <PageContainer>
      {/* Premium Hero Section */}
      <HeroSection>
        <PremiumCarousel
          autoplay
          dots
          effect="fade"
          autoplaySpeed={7000}
          pauseOnHover={false}
        >
          <div>
            <HeroSlide
              style={{
                backgroundImage: "url(/images/high-quality/diamond_workshop_hq.jpg)",
              }}
            >
              <HeroContent>
                <div style={{ marginBottom: "40px" }}>
                  <h1 style={{
                    color: "#d4af37",
                    fontSize: "2rem",
                    fontWeight: "bold",
                    margin: 0,
                    textShadow: "0 4px 8px rgba(0, 0, 0, 0.8)"
                  }}>
                    RAMAVATARGEMS
                  </h1>
                </div>
                <Title
                  level={1}
                  style={{
                    color: "#ffffff",
                    fontSize: "5rem",
                    fontWeight: 900,
                    marginBottom: "30px",
                    textShadow: "0 8px 16px rgba(0, 0, 0, 0.9)",
                    fontFamily: "'Playfair Display', serif",
                    lineHeight: 1.1,
                  }}
                >
                  Exquisite{" "}
                  <span style={{
                    color: "#d4af37",
                    textShadow: "0 0 40px rgba(212, 175, 55, 0.9)"
                  }}>
                    Diamond
                  </span>{" "}
                  Artistry
                </Title>
                <Text
                  style={{
                    color: "#ffffff",
                    fontSize: "1.8rem",
                    marginBottom: "60px",
                    textShadow: "0 4px 8px rgba(0, 0, 0, 0.8)",
                    maxWidth: "900px",
                    lineHeight: 1.8,
                    fontWeight: "300",
                    display: "block",
                    margin: "0 auto 60px",
                  }}
                >
                  Four decades of mastering the art of diamond jewelry manufacturing.
                  Where every diamond becomes a masterpiece of brilliance and beauty.
                </Text>
                <Space size="large" wrap style={{ justifyContent: "center" }}>
                  <AntButton
                    type="primary"
                    size="large"
                    style={{
                      background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 100%)",
                      border: "none",
                      color: "#000000",
                      padding: "0 60px",
                      height: "70px",
                      fontSize: "1.3rem",
                      fontWeight: "700",
                      borderRadius: "35px",
                      boxShadow: "0 15px 35px rgba(212, 175, 55, 0.6)",
                      transition: "all 0.3s ease",
                    }}
                  >
                    Explore Diamond Collection
                  </AntButton>
                  <AntButton
                    size="large"
                    style={{
                      background: "rgba(255, 255, 255, 0.1)",
                      border: "2px solid rgba(212, 175, 55, 0.6)",
                      color: "#ffffff",
                      padding: "0 60px",
                      height: "70px",
                      fontSize: "1.3rem",
                      fontWeight: "600",
                      borderRadius: "35px",
                      backdropFilter: "blur(20px)",
                      transition: "all 0.3s ease",
                    }}
                  >
                    Manufacturing Process
                  </AntButton>
                </Space>
              </HeroContent>
            </HeroSlide>
          </div>
        </PremiumCarousel>
      </HeroSection>

      {/* Premium Diamond Showcase */}
      <DiamondShowcase>
        <Section>
          <Container>
            <div style={{ textAlign: "center", marginBottom: "80px", position: "relative", zIndex: 2 }}>
              <Title
                level={2}
                style={{
                  color: "#ffffff",
                  fontSize: "4rem",
                  fontWeight: 900,
                  marginBottom: "30px",
                  fontFamily: "'Playfair Display', serif",
                  textShadow: "0 4px 8px rgba(0, 0, 0, 0.5)",
                }}
              >
                Our{" "}
                <span style={{ color: "#d4af37" }}>Diamond</span>{" "}
                Collections
              </Title>
              <Text
                style={{
                  color: "rgba(255, 255, 255, 0.9)",
                  fontSize: "1.4rem",
                  maxWidth: "800px",
                  margin: "0 auto",
                  lineHeight: 1.7,
                  display: "block",
                }}
              >
                Discover our exquisite range of diamond cuts, each crafted to perfection
                to maximize brilliance, fire, and scintillation.
              </Text>
            </div>

            <Row gutter={[40, 40]} style={{ position: "relative", zIndex: 2 }}>
              {diamondCollections.map((collection, index) => (
                <Col xs={24} sm={12} lg={6} key={index}>
                  <DiamondCard hoverable>
                    <ImageContainer>
                      <img
                        src={collection.image}
                        alt={collection.title}
                      />
                      <div
                        style={{
                          position: "absolute",
                          top: "20px",
                          right: "20px",
                          background: "rgba(212, 175, 55, 0.9)",
                          color: "#000000",
                          padding: "12px",
                          borderRadius: "50%",
                          fontSize: "1.5rem",
                          boxShadow: "0 4px 12px rgba(212, 175, 55, 0.4)",
                        }}
                      >
                        {collection.icon}
                      </div>
                    </ImageContainer>
                    <CardContent>
                      <Title
                        level={4}
                        style={{
                          color: "#ffffff",
                          marginBottom: "16px",
                          fontSize: "1.4rem",
                          fontWeight: "700",
                        }}
                      >
                        {collection.title}
                      </Title>
                      <Paragraph
                        style={{
                          color: "rgba(255, 255, 255, 0.8)",
                          fontSize: "1rem",
                          lineHeight: 1.6,
                          marginBottom: "24px",
                        }}
                      >
                        {collection.description}
                      </Paragraph>
                      <AntButton
                        type="link"
                        style={{
                          color: "#d4af37",
                          padding: "0",
                          fontSize: "1rem",
                          fontWeight: "600",
                        }}
                      >
                        Explore Collection <ArrowRightOutlined />
                      </AntButton>
                    </CardContent>
                  </DiamondCard>
                </Col>
              ))}
            </Row>
          </Container>
        </Section>
      </DiamondShowcase>

      {/* Premium Manufacturing Excellence */}
      <Section style={{ background: "linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%)" }}>
        <Container>
          <Row gutter={[60, 60]} align="middle">
            <Col xs={24} lg={12}>
              <div
                style={{
                  position: "relative",
                  borderRadius: "30px",
                  overflow: "hidden",
                  boxShadow: "0 30px 60px rgba(0, 0, 0, 0.5)",
                }}
              >
                <img
                  src="/images/manufacturing/diamond_cutting_1.jpg"
                  alt="Diamond Manufacturing Excellence"
                  style={{ width: "100%", height: "500px", objectFit: "cover" }}
                />
                <div
                  style={{
                    position: "absolute",
                    top: "30px",
                    left: "30px",
                    background: "rgba(212, 175, 55, 0.95)",
                    color: "#000000",
                    padding: "16px 24px",
                    borderRadius: "20px",
                    fontWeight: "700",
                    fontSize: "1.1rem",
                    boxShadow: "0 8px 20px rgba(212, 175, 55, 0.4)",
                  }}
                >
                  Since 1982
                </div>
              </div>
            </Col>
            <Col xs={24} lg={12}>
              <div style={{ paddingLeft: "40px" }}>
                <Title
                  level={2}
                  style={{
                    color: "#ffffff",
                    fontSize: "3.5rem",
                    fontWeight: 900,
                    marginBottom: "30px",
                    fontFamily: "'Playfair Display', serif",
                    lineHeight: 1.2,
                  }}
                >
                  Manufacturing{" "}
                  <span style={{ color: "#d4af37" }}>Excellence</span>
                </Title>
                <Paragraph
                  style={{
                    color: "rgba(255, 255, 255, 0.9)",
                    fontSize: "1.3rem",
                    lineHeight: 1.8,
                    marginBottom: "40px",
                  }}
                >
                  Four decades of expertise in diamond jewelry manufacturing. Our master
                  craftsmen combine traditional techniques with cutting-edge technology
                  to create diamonds that capture and reflect light with unparalleled brilliance.
                </Paragraph>
                <Space direction="vertical" size="large" style={{ width: "100%" }}>
                  <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
                    <div
                      style={{
                        width: "60px",
                        height: "60px",
                        background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 100%)",
                        borderRadius: "50%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: "1.5rem",
                        color: "#000000",
                        fontWeight: "bold",
                      }}
                    >
                      40+
                    </div>
                    <div>
                      <Text style={{ color: "#ffffff", fontSize: "1.2rem", fontWeight: "600", display: "block" }}>
                        Years of Excellence
                      </Text>
                      <Text style={{ color: "rgba(255, 255, 255, 0.7)", fontSize: "1rem" }}>
                        Crafting diamond jewelry since 1982
                      </Text>
                    </div>
                  </div>
                  <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
                    <div
                      style={{
                        width: "60px",
                        height: "60px",
                        background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 100%)",
                        borderRadius: "50%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: "1.2rem",
                        color: "#000000",
                        fontWeight: "bold",
                      }}
                    >
                      50K+
                    </div>
                    <div>
                      <Text style={{ color: "#ffffff", fontSize: "1.2rem", fontWeight: "600", display: "block" }}>
                        Diamonds Crafted
                      </Text>
                      <Text style={{ color: "rgba(255, 255, 255, 0.7)", fontSize: "1rem" }}>
                        Each one a masterpiece of precision
                      </Text>
                    </div>
                  </div>
                </Space>
                <div style={{ marginTop: "50px" }}>
                  <AntButton
                    type="primary"
                    size="large"
                    style={{
                      background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 100%)",
                      border: "none",
                      color: "#000000",
                      padding: "0 40px",
                      height: "60px",
                      fontSize: "1.1rem",
                      fontWeight: "700",
                      borderRadius: "30px",
                      boxShadow: "0 10px 25px rgba(212, 175, 55, 0.4)",
                    }}
                  >
                    Discover Our Process <ArrowRightOutlined />
                  </AntButton>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </Section>

      {/* Premium Portfolio Showcase */}
      <Section style={{ background: "linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)" }}>
        <Container>
          <div style={{ textAlign: "center", marginBottom: "80px" }}>
            <Title
              level={2}
              style={{
                color: "#ffffff",
                fontSize: "4rem",
                fontWeight: 900,
                marginBottom: "30px",
                fontFamily: "'Playfair Display', serif",
                textShadow: "0 4px 8px rgba(0, 0, 0, 0.5)",
              }}
            >
              Signature{" "}
              <span style={{ color: "#d4af37" }}>Diamond</span>{" "}
              Creations
            </Title>
            <Text
              style={{
                color: "rgba(255, 255, 255, 0.9)",
                fontSize: "1.4rem",
                maxWidth: "800px",
                margin: "0 auto",
                lineHeight: 1.7,
                display: "block",
              }}
            >
              Each piece in our collection represents the pinnacle of diamond jewelry craftsmanship,
              designed to capture and reflect light in the most spectacular way.
            </Text>
          </div>

          <Row gutter={[30, 30]}>
            <Col xs={24} md={12} lg={8}>
              <div
                style={{
                  position: "relative",
                  borderRadius: "25px",
                  overflow: "hidden",
                  height: "400px",
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.3)",
                  transition: "transform 0.4s ease",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = "translateY(-15px)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                <img
                  src="/images/jewelry_pieces/diamond_ring.jpg"
                  alt="Premium Diamond Ring"
                  style={{ width: "100%", height: "100%", objectFit: "cover" }}
                />
                <div
                  style={{
                    position: "absolute",
                    bottom: "0",
                    left: "0",
                    width: "100%",
                    background: "linear-gradient(to top, rgba(0,0,0,0.9) 0%, transparent 100%)",
                    padding: "40px 30px 30px",
                    color: "#ffffff",
                  }}
                >
                  <Title level={4} style={{ color: "#ffffff", marginBottom: "8px" }}>
                    Diamond Engagement Rings
                  </Title>
                  <Text style={{ color: "rgba(255, 255, 255, 0.8)" }}>
                    Timeless symbols of eternal love
                  </Text>
                </div>
              </div>
            </Col>
            <Col xs={24} md={12} lg={8}>
              <div
                style={{
                  position: "relative",
                  borderRadius: "25px",
                  overflow: "hidden",
                  height: "400px",
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.3)",
                  transition: "transform 0.4s ease",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = "translateY(-15px)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                <img
                  src="/images/jewelry_pieces/diamond_necklace.jpg"
                  alt="Premium Diamond Necklace"
                  style={{ width: "100%", height: "100%", objectFit: "cover" }}
                />
                <div
                  style={{
                    position: "absolute",
                    bottom: "0",
                    left: "0",
                    width: "100%",
                    background: "linear-gradient(to top, rgba(0,0,0,0.9) 0%, transparent 100%)",
                    padding: "40px 30px 30px",
                    color: "#ffffff",
                  }}
                >
                  <Title level={4} style={{ color: "#ffffff", marginBottom: "8px" }}>
                    Diamond Necklaces
                  </Title>
                  <Text style={{ color: "rgba(255, 255, 255, 0.8)" }}>
                    Elegant statements of sophistication
                  </Text>
                </div>
              </div>
            </Col>
            <Col xs={24} md={12} lg={8}>
              <div
                style={{
                  position: "relative",
                  borderRadius: "25px",
                  overflow: "hidden",
                  height: "400px",
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.3)",
                  transition: "transform 0.4s ease",
                  cursor: "pointer",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = "translateY(-15px)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                <img
                  src="/images/high-quality/diamond_cutting_hq.jpg"
                  alt="Diamond Cutting Process"
                  style={{ width: "100%", height: "100%", objectFit: "cover" }}
                />
                <div
                  style={{
                    position: "absolute",
                    bottom: "0",
                    left: "0",
                    width: "100%",
                    background: "linear-gradient(to top, rgba(0,0,0,0.9) 0%, transparent 100%)",
                    padding: "40px 30px 30px",
                    color: "#ffffff",
                  }}
                >
                  <Title level={4} style={{ color: "#ffffff", marginBottom: "8px" }}>
                    Master Craftsmanship
                  </Title>
                  <Text style={{ color: "rgba(255, 255, 255, 0.8)" }}>
                    Precision in every cut and setting
                  </Text>
                </div>
              </div>
            </Col>
          </Row>

          <div style={{ textAlign: "center", marginTop: "60px" }}>
            <AntButton
              type="primary"
              size="large"
              style={{
                background: "linear-gradient(135deg, #d4af37 0%, #f7e9b7 100%)",
                border: "none",
                color: "#000000",
                padding: "0 50px",
                height: "65px",
                fontSize: "1.2rem",
                fontWeight: "700",
                borderRadius: "32px",
                boxShadow: "0 15px 35px rgba(212, 175, 55, 0.5)",
              }}
            >
              View Complete Collection <ArrowRightOutlined />
            </AntButton>
          </div>
        </Container>
      </Section>
    </PageContainer>
  );
};

export default HomePremium;
