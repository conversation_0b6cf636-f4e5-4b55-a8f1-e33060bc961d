import React, { useRef, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';
import { ChevronLeft, ChevronRight, Sparkles, Diamond, ArrowRight } from 'lucide-react';
import Ramavatarge<PERSON>Logo from '../logo/RamavatargemsLogo';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

const DiamondCarousel = () => {
  const swiperRef = useRef(null);

  const slides = [
    {
      id: 1,
      backgroundImage: '/images/high-quality/diamond_workshop_hq.jpg',
      title: 'Crafting Timeless',
      highlightText: 'Diamond',
      subtitle: 'Elegance',
      description: 'With Precision & Passion Since 1982',
      primaryButton: 'Explore Our Craftsmanship',
      secondaryButton: 'View Our Diamond Process',
    },
    {
      id: 2,
      backgroundImage: '/images/high-quality/diamond_cutting_hq.jpg',
      title: 'Precision',
      highlightText: 'Diamond',
      subtitle: 'Craftsmanship',
      description: 'Master artisans transforming precious diamonds into timeless masterpieces with unparalleled skill and dedication.',
      primaryButton: 'Discover Our Expertise',
      secondaryButton: 'View Diamond Gallery',
    },
  ];

  return (
    <div className="relative h-screen min-h-[600px] overflow-hidden">
      <Swiper
        ref={swiperRef}
        modules={[Navigation, Pagination, Autoplay, EffectFade]}
        effect="fade"
        fadeEffect={{ crossFade: true }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        navigation={{
          prevEl: '.swiper-button-prev-custom',
          nextEl: '.swiper-button-next-custom',
        }}
        pagination={{
          el: '.swiper-pagination-custom',
          clickable: true,
          renderBullet: (index, className) => {
            return `<span class="${className} w-3 h-3 bg-white/50 rounded-full cursor-pointer transition-all duration-300 hover:bg-white/80"></span>`;
          },
        }}
        loop={true}
        speed={1000}
        className="h-full w-full"
      >
        {slides.map((slide) => (
          <SwiperSlide key={slide.id}>
            <div
              className="relative h-full w-full bg-cover bg-center bg-no-repeat flex items-center justify-center"
              style={{
                backgroundImage: `url(${slide.backgroundImage})`,
              }}
            >
              {/* Overlay Gradients */}
              <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-blue-900/60 to-black/80 z-10" />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent z-20" />
              
              {/* Animated Background Elements */}
              <div className="absolute inset-0 z-20">
                {[...Array(20)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-blue-300 rounded-full opacity-30 animate-float"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 3}s`,
                      animationDuration: `${3 + Math.random() * 2}s`,
                    }}
                  />
                ))}
              </div>
              
              {/* Content */}
              <div className="relative z-30 max-w-6xl mx-auto px-6 text-center">
                <div className="bg-black/30 backdrop-blur-xl rounded-3xl border border-white/20 p-8 md:p-12 shadow-2xl animate-fade-in">
                  {/* Logo */}
                  <div className="mb-8 filter drop-shadow-2xl animate-float">
                    <RamavatargemsLogo width={150} />
                  </div>
                  
                  {/* Title */}
                  <h1 className="text-4xl md:text-6xl lg:text-8xl font-bold font-playfair text-white mb-6 leading-tight">
                    {slide.title}{' '}
                    <span className="text-blue-300 animate-glow filter drop-shadow-lg relative">
                      {slide.highlightText}
                      <Sparkles className="absolute -top-2 -right-2 w-6 h-6 text-blue-200 animate-pulse" />
                    </span>{' '}
                    {slide.subtitle}
                  </h1>
                  
                  {/* Description */}
                  <p className="text-lg md:text-xl lg:text-2xl text-white/90 mb-10 max-w-4xl mx-auto leading-relaxed bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    {slide.description}
                  </p>
                  
                  {/* Buttons */}
                  <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                    <button className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 min-w-[250px] overflow-hidden">
                      <span className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                      <span className="relative flex items-center justify-center">
                        <Diamond className="mr-2 h-5 w-5" />
                        {slide.primaryButton}
                      </span>
                    </button>
                    
                    <button className="group relative px-8 py-4 border-2 border-blue-400 text-blue-400 bg-transparent hover:bg-blue-400 hover:text-white backdrop-blur-sm rounded-full font-semibold text-lg transition-all duration-300 min-w-[250px] overflow-hidden">
                      <span className="absolute inset-0 bg-blue-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                      <span className="relative flex items-center justify-center">
                        <ArrowRight className="mr-2 h-5 w-5" />
                        {slide.secondaryButton}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation */}
      <div className="swiper-button-prev-custom absolute left-8 top-1/2 -translate-y-1/2 z-40 w-12 h-12 bg-black/20 border border-white/20 text-white rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-600/30 hover:border-blue-400 backdrop-blur-sm transition-all duration-300 group">
        <ChevronLeft className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
      </div>
      
      <div className="swiper-button-next-custom absolute right-8 top-1/2 -translate-y-1/2 z-40 w-12 h-12 bg-black/20 border border-white/20 text-white rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-600/30 hover:border-blue-400 backdrop-blur-sm transition-all duration-300 group">
        <ChevronRight className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
      </div>

      {/* Custom Pagination */}
      <div className="swiper-pagination-custom absolute bottom-8 left-1/2 -translate-x-1/2 z-40 flex space-x-3"></div>
    </div>
  );
};

export default DiamondCarousel;
