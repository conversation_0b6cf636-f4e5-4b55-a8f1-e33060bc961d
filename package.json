{"name": "jewellery-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vite": "^6.3.5"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.1.0", "@radix-ui/react-slot": "^1.2.3", "aceternity-ui": "^0.2.2", "antd": "^5.25.1", "aos": "^2.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.14.0", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.0", "react-spring": "^10.0.1", "styled-components": "^6.1.18", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0"}}